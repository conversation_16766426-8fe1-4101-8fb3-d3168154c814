#!/bin/bash

# Setup HTTPS for Tuchanga Job Platform on AWS EC2
# This script sets up SSL certificates and configures HTTPS

set -e

echo "🔐 Setting up HTTPS for Tuchanga Job Platform..."

# Configuration
DOMAIN="ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"
SSL_DIR="./ssl"
EMAIL="<EMAIL>"  # Change this to your email

# Create SSL directory
mkdir -p $SSL_DIR

echo "📋 Choose SSL certificate option:"
echo "1. Self-signed certificate (Quick setup, browser warnings)"
echo "2. Let's Encrypt certificate (Recommended, requires domain access)"
echo "3. Manual certificate (Provide your own certificates)"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🔧 Generating self-signed certificate..."
        
        # Generate private key
        openssl genrsa -out $SSL_DIR/key.pem 2048
        
        # Generate certificate signing request
        openssl req -new -key $SSL_DIR/key.pem -out $SSL_DIR/cert.csr -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN"
        
        # Generate self-signed certificate
        openssl x509 -req -days 365 -in $SSL_DIR/cert.csr -signkey $SSL_DIR/key.pem -out $SSL_DIR/cert.pem
        
        # Clean up CSR
        rm $SSL_DIR/cert.csr
        
        echo "✅ Self-signed certificate generated!"
        echo "⚠️  Note: Browsers will show security warnings for self-signed certificates"
        ;;
        
    2)
        echo "🔧 Setting up Let's Encrypt certificate..."
        
        # Check if certbot is installed
        if ! command -v certbot &> /dev/null; then
            echo "📦 Installing certbot..."
            
            # Install certbot based on OS
            if [[ "$OSTYPE" == "linux-gnu"* ]]; then
                # Ubuntu/Debian
                sudo apt update
                sudo apt install -y certbot
            elif [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                brew install certbot
            else
                echo "❌ Please install certbot manually for your OS"
                exit 1
            fi
        fi
        
        echo "🌐 Obtaining Let's Encrypt certificate..."
        echo "⚠️  Make sure your domain $DOMAIN points to this server"
        echo "⚠️  Port 80 must be accessible from the internet"
        
        read -p "Continue? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            echo "❌ Aborted"
            exit 1
        fi
        
        # Stop any running containers to free port 80
        echo "🛑 Stopping containers to free port 80..."
        docker-compose down || true
        
        # Obtain certificate
        sudo certbot certonly --standalone \
            --email $EMAIL \
            --agree-tos \
            --no-eff-email \
            -d $DOMAIN
        
        # Copy certificates to our SSL directory
        sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $SSL_DIR/cert.pem
        sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $SSL_DIR/key.pem
        
        # Fix permissions
        sudo chown $(whoami):$(whoami) $SSL_DIR/cert.pem $SSL_DIR/key.pem
        
        echo "✅ Let's Encrypt certificate obtained!"
        ;;
        
    3)
        echo "📁 Manual certificate setup..."
        echo "Please place your certificate files in the $SSL_DIR directory:"
        echo "  - Certificate: $SSL_DIR/cert.pem"
        echo "  - Private key: $SSL_DIR/key.pem"
        
        read -p "Press Enter when files are ready..."
        
        if [[ ! -f "$SSL_DIR/cert.pem" ]] || [[ ! -f "$SSL_DIR/key.pem" ]]; then
            echo "❌ Certificate files not found!"
            exit 1
        fi
        
        echo "✅ Manual certificates configured!"
        ;;
        
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

# Verify certificate files exist
if [[ ! -f "$SSL_DIR/cert.pem" ]] || [[ ! -f "$SSL_DIR/key.pem" ]]; then
    echo "❌ SSL certificate files not found!"
    exit 1
fi

echo "🔍 Verifying certificate files..."
openssl x509 -in $SSL_DIR/cert.pem -text -noout > /dev/null
echo "✅ Certificate files are valid!"

# Set proper permissions
chmod 644 $SSL_DIR/cert.pem
chmod 600 $SSL_DIR/key.pem

echo "🚀 Starting application with HTTPS..."
docker-compose up -d --build

echo ""
echo "🎉 HTTPS setup complete!"
echo ""
echo "🌐 Your application is now available at:"
echo "   https://$DOMAIN"
echo ""
echo "📋 Next steps:"
echo "   1. Wait for containers to start (check with: docker-compose ps)"
echo "   2. Test the HTTPS connection"
echo "   3. Update your DNS if needed"
echo ""

if [[ $choice == "2" ]]; then
    echo "🔄 Let's Encrypt certificate renewal:"
    echo "   Certificates expire in 90 days"
    echo "   Set up auto-renewal with: sudo crontab -e"
    echo "   Add: 0 12 * * * /usr/bin/certbot renew --quiet"
    echo ""
fi

echo "✅ Setup complete!"
