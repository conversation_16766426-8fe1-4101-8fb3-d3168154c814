#!/usr/bin/env node

/**
 * Simple health check script for ChanguApp Backend
 * This script can be used by systemd, monitoring tools, or manual checks
 */

const http = require('http');

const CONFIG = {
  host: 'localhost',
  port: process.env.PORT || 3000,
  path: '/health',
  timeout: 5000, // 5 seconds
};

function healthCheck() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: CONFIG.host,
      port: CONFIG.port,
      path: CONFIG.path,
      method: 'GET',
      timeout: CONFIG.timeout,
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            resolve({
              status: 'healthy',
              statusCode: res.statusCode,
              response: response,
              timestamp: new Date().toISOString(),
            });
          } catch (err) {
            resolve({
              status: 'healthy',
              statusCode: res.statusCode,
              response: data,
              timestamp: new Date().toISOString(),
            });
          }
        } else {
          reject({
            status: 'unhealthy',
            statusCode: res.statusCode,
            response: data,
            timestamp: new Date().toISOString(),
          });
        }
      });
    });

    req.on('error', (err) => {
      reject({
        status: 'error',
        error: err.message,
        timestamp: new Date().toISOString(),
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        status: 'timeout',
        error: `Request timed out after ${CONFIG.timeout}ms`,
        timestamp: new Date().toISOString(),
      });
    });

    req.end();
  });
}

// Run health check
if (require.main === module) {
  console.log(`🔍 Checking health of ChanguApp Backend at http://${CONFIG.host}:${CONFIG.port}${CONFIG.path}`);
  
  healthCheck()
    .then((result) => {
      console.log('✅ Health check passed:');
      console.log(JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch((error) => {
      console.log('❌ Health check failed:');
      console.log(JSON.stringify(error, null, 2));
      process.exit(1);
    });
}

module.exports = { healthCheck };
