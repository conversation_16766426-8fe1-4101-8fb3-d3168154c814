// Environment configuration for the frontend application
export interface Environment {
  apiBaseUrl: string;
  environment: 'development' | 'production' | 'test';
}

// Get the API base URL based on the environment
const getApiBaseUrl = (): string => {
  // Check for explicit environment variable first
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }

  // Check if we're in development mode (Vite dev server)
  if (import.meta.env.DEV) {
    return 'http://localhost:3000';
  }

  // In production, use the current document's href as base
  // Extract protocol and host from current URL
  const currentUrl = new URL(window.location.href);
  const protocol = currentUrl.protocol; // http: or https:
  const hostname = currentUrl.hostname; // e.g., ************* or localhost

  // For production, use the same protocol and host with port 3000
  return `${protocol}//${hostname}:3000`;
};

// Environment configuration
export const environment: Environment = {
  apiBaseUrl: getApiBaseUrl(),
  environment: import.meta.env.DEV ? 'development' : 'production',
};

// Export the API base URL for convenience
export const API_BASE_URL = environment.apiBaseUrl;
