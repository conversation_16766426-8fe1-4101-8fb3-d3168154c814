import axios from 'axios';
import { auth } from '../config/firebase';
import { API_BASE_URL } from '../config/environment';

// Auth API functions
export const registerUserWithBackend = async (firebaseToken: string, userData: {
  firstName: string;
  lastName: string;
  phone?: string;
}) => {
  const response = await fetch(`${API_BASE_URL}/auth/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      firebaseToken,
      ...userData,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw { response: { data: errorData } };
  }

  return response.json();
};

export const loginUserWithBackend = async (firebaseToken: string) => {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      firebaseToken,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw { response: { data: errorData } };
  }

  return response.json();
};

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include Firebase token
api.interceptors.request.use(
  async (config) => {
    const user = auth.currentUser;
    if (user) {
      try {
        const token = await user.getIdToken();
        config.headers.Authorization = `Bearer ${token}`;
      } catch (error) {
        console.error('Error getting Firebase token:', error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  email: string;
  firebaseUid: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'worker' | 'employer' | 'both';
  profilePicture?: string;
  bio?: string;
  skills?: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  hourlyRate?: number;
  isAvailable: boolean;
  isVip: boolean;
  completedJobs: number;
  postedJobsCompleted?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ContactRequest {
  id: string;
  requester: User;
  requesterId: string;
  jobPoster: User;
  jobPosterId: string;
  job: Job;
  jobId: string;
  status: 'pending' | 'accepted' | 'rejected';
  message: string;
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  id: string;
  user1: User;
  user2: User;
  user1Id: string;
  user2Id: string;
  job: {
    id: string;
    title: string;
    status: string;
    employerId: string;
  };
  lastMessageAt: string;
  lastMessagePreview: string;
  unreadCountUser1: number;
  unreadCountUser2: number;
  isClosed: boolean;
  closedByUserId?: string;
  closedAt?: string;
  lastMessage?: {
    content: string;
    senderId: string;
    createdAt: string;
  };
}

export type PaymentMethod = 'cash' | 'bank_transfer';

export interface Job {
  id: string;
  title: string;
  description: string;
  category: 'home_maintenance' | 'gardening' | 'cleaning' | 'painting' | 'plumbing' | 'electrical' | 'moving' | 'delivery' | 'tutoring' | 'pet_care' | 'childcare' | 'cooking' | 'tech_support' | 'handyman' | 'carpentry' | 'elderly_care' | 'other';
  budget: number; // Changed from string to number to match backend
  location: string;
  latitude: number; // Changed from string to number to match backend
  longitude: number; // Changed from string to number to match backend
  status: 'open' | 'in_progress' | 'completed' | 'cancelled' | 'paused';
  requiredSkills?: string;
  estimatedHours?: number; // Changed from string to number and made optional to match backend
  startDate?: string;
  deadline?: string;
  isUrgent: boolean;
  images?: string;
  icon?: string;
  paymentMethods: PaymentMethod[];
  createdAt: string;
  updatedAt: string;
  employer: User;
  employerId: string;
  completedBy?: User;
  completedById?: string;
  completedAt?: string;
}

// Job categories mapping to backend enum with automatic icons
export const jobCategories = [
  { value: 'home_maintenance', label: 'Mantenimiento del hogar', emoji: '🏠' },
  { value: 'gardening', label: 'Jardinería', emoji: '🌱' },
  { value: 'cleaning', label: 'Limpieza', emoji: '🧹' },
  { value: 'painting', label: 'Pintura', emoji: '🎨' },
  { value: 'plumbing', label: 'Plomería', emoji: '🔧' },
  { value: 'electrical', label: 'Electricidad', emoji: '⚡' },
  { value: 'moving', label: 'Mudanzas', emoji: '📦' },
  { value: 'delivery', label: 'Delivery', emoji: '🚚' },
  { value: 'tutoring', label: 'Clases/Tutorías', emoji: '📚' },
  { value: 'pet_care', label: 'Cuidado de mascotas', emoji: '🐕' },
  { value: 'childcare', label: 'Cuidado de niños', emoji: '👶' },
  { value: 'cooking', label: 'Cocina', emoji: '👨‍🍳' },
  { value: 'tech_support', label: 'Soporte técnico', emoji: '💻' },
  { value: 'handyman', label: 'Trabajos generales', emoji: '🔨' },
  { value: 'carpentry', label: 'Carpintería', emoji: '🪚' },
  { value: 'elderly_care', label: 'Cuidado de adultos mayores', emoji: '👴' },
  { value: 'other', label: 'Otros', emoji: '⚙️' },
]

// Helper function to get icon by category
export const getCategoryIcon = (category: string): string => {
  const categoryData = jobCategories.find(cat => cat.value === category)
  return categoryData?.emoji || '⚙️'
}

// Payment methods mapping
export const paymentMethods = [
  { value: 'cash' as PaymentMethod, label: 'Dinero en efectivo', emoji: '💵' },
  { value: 'bank_transfer' as PaymentMethod, label: 'Transferencia bancaria', emoji: '🏦' },
]

// Helper function to get payment method label
export const getPaymentMethodLabel = (method: PaymentMethod): string => {
  const methodData = paymentMethods.find(pm => pm.value === method)
  return methodData?.label || method
}

// Helper function to get payment method emoji
export const getPaymentMethodEmoji = (method: PaymentMethod): string => {
  const methodData = paymentMethods.find(pm => pm.value === method)
  return methodData?.emoji || '💳'
}

export interface JobTemplate {
  id: string;
  title: string;
  description: string;
  category: 'home_maintenance' | 'gardening' | 'cleaning' | 'painting' | 'plumbing' | 'electrical' | 'moving' | 'delivery' | 'tutoring' | 'pet_care' | 'childcare' | 'cooking' | 'tech_support' | 'handyman' | 'carpentry' | 'elderly_care' | 'other';
  budget: number;
  location: string;
  latitude: number;
  longitude: number;
  requiredSkills?: string;
  estimatedHours?: number;
  isUrgent: boolean;
  images?: string;
  icon?: string;
  paymentMethods: PaymentMethod[];
  createdAt: string;
  userId: string;
}

export interface JobsResponse {
  jobs: Job[];
  total: number;
  page: number;
  totalPages: number;
}



// API functions
export const apiService = {
  // Health check
  async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  },



  // Get jobs with pagination and filters
  async getJobs(params?: {
    page?: number;
    limit?: number;
    category?: string;
    status?: string;
  }): Promise<JobsResponse> {
    const response = await api.get('/jobs', { params });
    return response.data;
  },

  // Get single job by ID
  async getJob(id: string): Promise<Job> {
    const response = await api.get(`/jobs/${id}`);
    return response.data;
  },

  // Search jobs
  async searchJobs(params: {
    searchTerm?: string;
    category?: string;
    minBudget?: number;
    maxBudget?: number;
  }): Promise<Job[]> {
    const response = await api.get('/jobs/search', { params });
    return response.data;
  },

  // Get nearby jobs
  async getNearbyJobs(params: {
    latitude: number;
    longitude: number;
    radiusKm?: number;
    category?: string;
  }): Promise<Job[]> {
    const response = await api.get('/jobs/nearby', { params });
    return response.data;
  },

  // Get jobs with location filtering
  async getJobsWithLocation(params?: {
    page?: number;
    limit?: number;
    category?: string;
    status?: string;
    latitude?: number;
    longitude?: number;
    radiusKm?: number;
  }): Promise<JobsResponse> {
    const response = await api.get('/jobs', { params });
    return response.data;
  },

  // Create new job (requires authentication)
  async createJob(jobData: Partial<Job>): Promise<Job> {
    console.log('📡 API Service: Creating job with data:', jobData);
    console.log('🔐 API Service: Current auth token exists:', !!api.defaults.headers.common['Authorization']);

    try {
      const response = await api.post('/jobs', jobData);
      console.log('✅ API Service: Job creation successful:', response.data);
      return response.data;
    } catch (error) { 
      console.error('❌ API Service: Job creation failed:', error);
      throw error;
    }
  },

  // Update job (requires authentication)
  async updateJob(id: string, jobData: Partial<Job>): Promise<Job> {
    const response = await api.patch(`/jobs/${id}`, jobData);
    return response.data;
  },

  // Delete job (requires authentication)
  async deleteJob(id: string): Promise<void> {
    await api.delete(`/jobs/${id}`);
  },

  // Get user's posted jobs (requires authentication)
  async getMyJobs(): Promise<Job[]> {
    const response = await api.get('/jobs/my-jobs');
    return response.data;
  },

  // Job management actions
  async pauseJob(id: string): Promise<Job> {
    const response = await api.patch(`/jobs/${id}/pause`);
    return response.data;
  },

  async resumeJob(id: string): Promise<Job> {
    const response = await api.patch(`/jobs/${id}/resume`);
    return response.data;
  },

  async completeJob(id: string, completionData?: { completedById?: string; completionType: 'completed' | 'no_agreement' }): Promise<Job> {
    const response = await api.patch(`/jobs/${id}/complete`, completionData || { completionType: 'completed' });
    return response.data;
  },

  // Get accepted contact requests for a job
  async getAcceptedContactRequestsForJob(jobId: string): Promise<ContactRequest[]> {
    const response = await api.get(`/chat/jobs/${jobId}/accepted-contacts`);
    return response.data;
  },

  // Get total contact request count for a job
  async getContactRequestCountForJob(jobId: string): Promise<{ count: number }> {
    const response = await api.get(`/chat/jobs/${jobId}/contact-count`);
    return response.data;
  },

  // Close a conversation
  async closeConversation(conversationId: string): Promise<Conversation> {
    const response = await api.patch(`/chat/conversations/${conversationId}/close`);
    return response.data;
  },

  // Get user profile (requires authentication)
  async getUserProfile(): Promise<User> {
    const response = await api.get('/users/profile');
    return response.data;
  },

  // Get user by ID (public endpoint)
  async getUserById(userId: string): Promise<User> {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  // Update user profile (requires authentication)
  async updateUserProfile(userData: Partial<User>): Promise<User> {
    const response = await api.put('/users/profile', userData);
    return response.data;
  },
};

// Notification API functions
export const notificationService = {
  // Get user notifications
  async getNotifications(params?: { limit?: number; offset?: number }) {
    const response = await api.get('/notifications', { params });
    return response.data;
  },

  // Get unread notification count
  async getUnreadCount() {
    const response = await api.get('/notifications/unread-count');
    return response.data;
  },

  // Mark notification as read
  async markAsRead(notificationId: string) {
    const response = await api.patch(`/notifications/${notificationId}/read`);
    return response.data;
  },

  // Mark all notifications as read
  async markAllAsRead() {
    const response = await api.patch('/notifications/mark-all-read');
    return response.data;
  },

  // Delete notification
  async deleteNotification(notificationId: string) {
    await api.delete(`/notifications/${notificationId}`);
  },
};



// Job Template API functions
export const templateService = {
  // Get user's job templates
  async getTemplates(): Promise<JobTemplate[]> {
    const response = await api.get('/jobs/templates/my');
    return response.data;
  },

  // Get single template by ID
  async getTemplate(templateId: string): Promise<JobTemplate> {
    const response = await api.get(`/jobs/templates/${templateId}`);
    return response.data;
  },

  // Delete template by ID
  async deleteTemplate(templateId: string): Promise<void> {
    await api.delete(`/jobs/templates/${templateId}`);
  },
};

export default api;
