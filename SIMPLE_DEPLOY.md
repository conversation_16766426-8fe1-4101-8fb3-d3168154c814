# TuChanga - Simple Deployment Guide

## 🚀 One-Command Deployment on Clean Instance

### Prerequisites
- Fresh EC2 instance with <PERSON><PERSON> and <PERSON>er Compose installed
- Git installed

### Quick Start (Clean Instance)

1. **Clone the repository:**
```bash
git clone your-repo-url tuchanga
cd tuchanga
```

2. **Deploy with one command:**
```bash
docker compose up -d --build
```

That's it! 🎉

### What happens automatically:

1. **PostgreSQL** starts first with the correct credentials from `.env`
2. **Backend** waits for PostgreSQL to be healthy before starting
3. **Frontend** starts after backend is ready
4. **Database initialization** runs automatically via `backend/init-scripts/01-init.sql`

### Access your application:

- **Frontend**: http://your-ec2-ip:80
- **Backend API**: http://your-ec2-ip:3000
- **Health Check**: http://your-ec2-ip:3000/health

### Configuration Details:

#### Automatic Resource Limits (for t3.micro):
- **PostgreSQL**: 20% CPU, 200MB RAM
- **Backend**: 40% CPU, 400MB RAM  
- **Frontend**: 10% CPU, 100MB RAM
- **Total**: 70% CPU, 700MB RAM (safe for 1GB instance)

#### Database Credentials (from `.env`):
```
POSTGRES_DB=job_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
```

### Monitoring:

```bash
# Check all services
docker compose ps

# Check logs
docker compose logs backend
docker compose logs postgres
docker compose logs frontend

# Monitor resources
docker stats
```

### Troubleshooting:

If something goes wrong:

```bash
# Stop everything
docker compose down

# Clean restart
docker compose down -v
docker compose up -d --build
```

### Why it works now:

1. **Consistent credentials** - `.env` file ensures all services use the same database credentials
2. **Proper health checks** - Backend waits for PostgreSQL to be fully ready
3. **Resource limits** - Prevents the instance from hanging due to resource exhaustion
4. **Database initialization** - PostgreSQL runs init scripts automatically on first start
5. **Sequential startup** - Services start in the correct order via `depends_on` with health checks

### Files that make it work:

- **`.env`** - Consistent environment variables
- **`docker-compose.yml`** - Proper service dependencies and resource limits
- **`backend/init-scripts/01-init.sql`** - Database initialization
- **`backend/Dockerfile`** - Optimized backend build
- **`frontend/Dockerfile`** - Optimized frontend build with nginx config
- **`frontend/nginx.conf`** - Nginx configuration for React SPA

No auxiliary scripts needed! 🎯
