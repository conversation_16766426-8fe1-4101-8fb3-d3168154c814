# Docker Compose for local development
# For production deployment, use docker-stack.yml with Docker Swarm

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: job-platform-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '0.2'      # 20% of CPU
          memory: 200M     # 200MB RAM
        reservations:
          cpus: '0.1'      # Reserve 10% CPU
          memory: 100M     # Reserve 100MB RAM

  # NestJS Backend API with PostgreSQL
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: job-platform-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: ${NODE_ENV}
      HOST: 0.0.0.0
      PORT: 3000
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DATABASE: ${DB_DATABASE}
      DATABASE_URL: ${DATABASE_URL}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      CORS_ORIGIN: ${CORS_ORIGIN}
      API_PREFIX: ${API_PREFIX}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD", "node", "/app/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 180s
    deploy:
      resources:
        limits:
          cpus: '0.4'      # 40% of CPU
          memory: 400M     # 400MB RAM
        reservations:
          cpus: '0.2'      # Reserve 20% CPU
          memory: 200M     # Reserve 200MB RAM

  # Frontend (React + Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: job-platform-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro  # Mount SSL certificates
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '0.1'      # 10% of CPU
          memory: 100M     # 100MB RAM
        reservations:
          cpus: '0.05'     # Reserve 5% CPU
          memory: 50M      # Reserve 50MB RAM

volumes:
  postgres_data:

networks:
  job-platform-network:
    driver: bridge
