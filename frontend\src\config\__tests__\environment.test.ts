import { describe, it, expect, vi, beforeEach } from 'vitest'

// Create a simple test to verify the environment configuration works
// Since mocking import.meta is complex in Vitest, we'll test the actual behavior

describe('Environment Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should export API_BASE_URL', async () => {
    const { API_BASE_URL } = await import('../environment')

    expect(API_BASE_URL).toBeDefined()
    expect(typeof API_BASE_URL).toBe('string')
    expect(API_BASE_URL).toMatch(/^https?:\/\//)
  })

  it('should export environment configuration', async () => {
    const { environment } = await import('../environment')

    expect(environment).toBeDefined()
    expect(environment).toHaveProperty('apiBaseUrl')
    expect(environment).toHaveProperty('environment')
    expect(['development', 'production', 'test']).toContain(environment.environment)
  })

  it('should use URL constructor for parsing location', () => {
    // Test that the URL constructor is used correctly
    const testUrl = 'http://*************/'
    const url = new URL(testUrl)

    expect(url.protocol).toBe('http:')
    expect(url.hostname).toBe('*************')

    // Test the expected format
    const expectedApiUrl = `${url.protocol}//${url.hostname}:3000`
    expect(expectedApiUrl).toBe('http://*************:3000')
  })

  it('should handle HTTPS protocol correctly', () => {
    const testUrl = 'https://example.com/'
    const url = new URL(testUrl)

    expect(url.protocol).toBe('https:')
    expect(url.hostname).toBe('example.com')

    const expectedApiUrl = `${url.protocol}//${url.hostname}:3000`
    expect(expectedApiUrl).toBe('https://example.com:3000')
  })

  it('should handle different hostnames correctly', () => {
    const testCases = [
      { input: 'http://localhost/', expected: 'http://localhost:3000' },
      { input: 'https://mydomain.com/', expected: 'https://mydomain.com:3000' },
      { input: 'http://*************/', expected: 'http://*************:3000' }
    ]

    testCases.forEach(({ input, expected }) => {
      const url = new URL(input)
      const result = `${url.protocol}//${url.hostname}:3000`
      expect(result).toBe(expected)
    })
  })
})
