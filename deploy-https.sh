#!/bin/bash

# One-command HTTPS deployment for Tuchanga Job Platform
# This script sets up everything needed for HTTPS deployment

set -e

echo "🚀 Tuchanga Job Platform - HTTPS Deployment"
echo "============================================"

# Check if SSL certificates exist
if [[ -f "ssl/cert.pem" && -f "ssl/key.pem" ]]; then
    echo "✅ SSL certificates found"
else
    echo "🔐 SSL certificates not found. Generating self-signed certificates..."
    
    # Create SSL directory
    mkdir -p ssl
    
    # Generate self-signed certificate
    openssl genrsa -out ssl/key.pem 2048
    openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 \
        -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"
    
    # Set permissions
    chmod 644 ssl/cert.pem
    chmod 600 ssl/key.pem
    
    echo "✅ Self-signed certificates generated"
    echo "⚠️  Note: Browsers will show security warnings"
fi

# Stop any running containers
echo "🛑 Stopping existing containers..."
docker-compose down || true

# Build and start with HTTPS
echo "🔨 Building and starting containers..."
docker-compose up -d --build

# Wait for containers to be ready
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo "📊 Container status:"
docker-compose ps

# Test HTTPS connection
echo "🧪 Testing HTTPS connection..."
if curl -k -s https://localhost:443 > /dev/null; then
    echo "✅ HTTPS connection successful"
else
    echo "⚠️  HTTPS connection test failed (this might be normal during startup)"
fi

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "🌐 Your application is available at:"
echo "   https://ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"
echo ""
echo "📋 Useful commands:"
echo "   docker-compose ps          # Check container status"
echo "   docker-compose logs        # View all logs"
echo "   docker-compose logs frontend  # View frontend logs"
echo "   docker-compose logs backend   # View backend logs"
echo ""
echo "⚠️  If using self-signed certificates:"
echo "   - Browsers will show security warnings"
echo "   - Click 'Advanced' → 'Proceed to site' to continue"
echo ""
echo "✅ Setup complete!"
