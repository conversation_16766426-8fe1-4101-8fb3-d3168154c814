{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:production": "node start-production.js", "health": "node health-check.js", "dev": "nest start --watch", "dev:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:cov": "cross-env NODE_ENV=test jest --coverage", "test:debug": "cross-env NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=test-e2e jest --config ./test/jest-e2e.json", "seed:dev": "ts-node -r tsconfig-paths/register src/database/seeds/run-seeds.ts", "db:clear": "ts-node -r tsconfig-paths/register src/database/seeds/clear-database.ts", "db:migrate": "ts-node -r tsconfig-paths/register src/database/migrations/run-migrations.ts", "db:migrate:prod": "node dist/src/database/migrations/run-migrations.js", "db:setup-test": "ts-node -r tsconfig-paths/register scripts/setup-test-db.ts", "test:setup": "npm run db:setup-test", "test:full": "npm run test:setup && npm run test && npm run test:e2e"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/multer": "^1.4.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "firebase-admin": "^13.4.0", "multer": "^2.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.15.2", "@types/supertest": "^6.0.2", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/../test/setup-tests.ts"], "testEnvironmentOptions": {"NODE_ENV": "test"}}}