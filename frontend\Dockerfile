# Etapa 1: Build del proyecto con Node 22 Alpine (smaller image)
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for better performance and install dependencies
RUN npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm config set fetch-retries 3 && \
    npm config set maxsockets 1 && \
    npm ci --no-audit --no-fund --maxsockets=1

# Copy source code and build
COPY . .
RUN npm run build

# Etapa 2: Servir el build con nginx (much smaller than Node.js)
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
