# ChanguApp Production Environment Variables for Docker Compose
# =============================================================

# Application Configuration
NODE_ENV=production

# Database Configuration (for Docker Compose)
POSTGRES_DB=job_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=ChanguApp2024SecureDB

# JWT Configuration
JWT_SECRET=ChanguApp2024SuperSecretJWTKeyChangeInProduction
JWT_EXPIRES_IN=24h

# Firebase Configuration
FIREBASE_PROJECT_ID=tu-changa-583b3

# CORS Configuration
CORS_ORIGIN=*

# API Configuration
API_PREFIX=

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=ChanguApp2024!SessionSecret!ChangeInProduction

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# AWS Configuration (if needed)
# AWS_REGION=sa-east-1
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# S3_BUCKET_NAME=changuapp-uploads

# Monitoring Configuration
HEALTH_CHECK_ENDPOINT=/health
METRICS_ENDPOINT=/metrics

# Production Optimizations
NODE_OPTIONS=--max-old-space-size=1024
