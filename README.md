# TuChanga - Plataforma de Trabajos Casuales

Una plataforma fullstack para trabajos casuales y de corto plazo como jardinería, paseo de perros, reparaciones y trabajos de mantenimiento general.

## 🏗️ Arquitectura

- **Frontend**: React + TypeScript + Vite + Chakra UI
- **Backend**: NestJS + TypeScript + PostgreSQL
- **Autenticación**: Firebase Auth
- **Deployment**: Docker Compose

## 🚀 Inicio Rápido

### Prerrequisitos
- Docker y Docker Compose instalados
- Node.js 22+ (solo para desarrollo local)

### 1. Clonar el Repositorio
```bash
git clone <repository-url>
cd tuchanga
```

### 2. Deployment con Docker Compose
```bash
docker compose up -d --build
```

¡Eso es todo! La aplicación estará disponible en:
- **Frontend**: http://localhost:80
- **Backend API**: http://localhost:3000
- **Health Check**: http://localhost:3000/health

## 📁 Estructura del Proyecto

```
tuchanga/
├── docker-compose.yml          # Configuración de Docker
├── .env                       # Variables de entorno
├── frontend/                   # Aplicación React
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── backend/                    # API NestJS
│   ├── src/
│   ├── Dockerfile
│   ├── init-scripts/          # Scripts de inicialización de DB
│   └── package.json
├── SIMPLE_DEPLOY.md           # Guía de deployment
└── test-clean-deploy.sh       # Script de prueba
```

## ⚙️ Configuración

El archivo `.env` ya está incluido con la configuración por defecto:
```env
POSTGRES_DB=job_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres123
DB_DATABASE=job_platform
NODE_ENV=production
JWT_SECRET=TuChanga2024SuperSecretJWTKeyChangeInProduction
JWT_EXPIRES_IN=24h
FIREBASE_PROJECT_ID=tu-changa-583b3
CORS_ORIGIN=*
```

## 🔧 Desarrollo Local

### Opción 1: Todo con Docker (Recomendado)
```bash
docker compose up -d --build
```

### Opción 2: Desarrollo Híbrido
```bash
# Solo base de datos con Docker
docker compose up postgres -d

# Frontend local
cd frontend
npm install
npm run dev  # http://localhost:5173

# Backend local
cd backend
npm install
npm run start:dev  # http://localhost:3000
```

## 🚀 Deployment en Producción

### En cualquier servidor con Docker:
```bash
git clone <repository-url> tuchanga
cd tuchanga
docker compose up -d --build
```

### Verificar deployment:
```bash
# Verificar servicios
docker compose ps

# Verificar logs
docker compose logs backend
docker compose logs frontend
docker compose logs postgres

# Probar endpoints
curl http://localhost:3000/health
curl http://localhost:80
```

## 🛠️ Comandos Útiles

### Docker
```bash
# Ver servicios corriendo
docker compose ps

# Ver logs
docker compose logs backend
docker compose logs frontend
docker compose logs postgres

# Parar servicios
docker compose down

# Reiniciar con rebuild
docker compose up -d --build

# Limpiar todo y empezar de nuevo
docker compose down -v
docker compose up -d --build
```

### Base de Datos
```bash
# Conectar a PostgreSQL
docker compose exec postgres psql -U postgres -d job_platform

# Backup
docker compose exec postgres pg_dump -U postgres job_platform > backup.sql

# Restore
docker compose exec -T postgres psql -U postgres job_platform < backup.sql
```

### Monitoreo
```bash
# Ver uso de recursos
docker stats

# Probar script de deployment limpio
chmod +x test-clean-deploy.sh
./test-clean-deploy.sh
```

## 🔐 Configuración de Producción

Para producción, actualiza estas variables en `.env`:
- `JWT_SECRET`: Usa una clave segura
- `POSTGRES_PASSWORD`: Usa una contraseña segura
- `FIREBASE_PROJECT_ID`: Tu proyecto de Firebase
- `CORS_ORIGIN`: Tu dominio específico

## 📚 Documentación

- [Guía de Deployment Simple](./SIMPLE_DEPLOY.md)
- [Backend README](./backend/README.md)
- [Frontend README](./frontend/README.md)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request
