FROM node:22-alpine

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for better performance and memory usage
RUN npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm config set fetch-retries 3 && \
    npm config set maxsockets 1 && \
    npm config set progress false

# Install dependencies with memory optimization and timeout
RUN timeout 600 npm ci --no-audit --no-fund --maxsockets=1 --prefer-offline || \
    (echo "npm ci failed, retrying..." && npm ci --no-audit --no-fund --maxsockets=1)

# Copy source code
COPY . .

# Build the application with timeout
RUN timeout 300 npm run build || (echo "Build failed, retrying..." && npm run build)

# Expose port
EXPOSE 3000

# Health check with proper path
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD node /app/health-check.js || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/src/main.js"]
