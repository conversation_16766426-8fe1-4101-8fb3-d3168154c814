#!/bin/bash

# Quick SSL certificate generation for development/testing
# Generates self-signed certificates for immediate HTTPS setup

set -e

echo "🔐 Generating self-signed SSL certificates..."

# Configuration
DOMAIN="ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"
SSL_DIR="./ssl"

# Create SSL directory
mkdir -p $SSL_DIR

# Generate private key
echo "🔑 Generating private key..."
openssl genrsa -out $SSL_DIR/key.pem 2048

# Generate certificate
echo "📜 Generating certificate..."
openssl req -new -x509 -key $SSL_DIR/key.pem -out $SSL_DIR/cert.pem -days 365 \
    -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=$DOMAIN"

# Set proper permissions
chmod 644 $SSL_DIR/cert.pem
chmod 600 $SSL_DIR/key.pem

echo "✅ SSL certificates generated successfully!"
echo "📁 Files created:"
echo "   - Certificate: $SSL_DIR/cert.pem"
echo "   - Private key: $SSL_DIR/key.pem"
echo ""
echo "⚠️  Note: These are self-signed certificates"
echo "   Browsers will show security warnings"
echo "   Click 'Advanced' -> 'Proceed to site' to continue"
echo ""
echo "🚀 Ready to start with HTTPS!"
echo "   Run: docker-compose up -d --build"
