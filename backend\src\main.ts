import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
// Initialize Firebase Admin SDK
import './config/firebase.config';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Serve static files for uploaded images
  app.useStaticAssets(join(__dirname, '..', 'uploads'), {
    prefix: '/uploads/',
  });

  // Enable CORS for frontend communication
  app.enableCors({
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:3000',
      'http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com',
      'http://*************',
      'http://**************', // Current EC2 public IP
      'http://ec2-18-229-140-118.sa-east-1.compute.amazonaws.com'
    ], // Vite dev server, backend API, and EC2 instance
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Swagger API documentation
  const config = new DocumentBuilder()
    .setTitle('Job Platform API')
    .setDescription('A comprehensive job platform API for connecting employers and employees')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 3000;
  const host = process.env.HOST || '0.0.0.0'; // Bind to all interfaces for Docker
  await app.listen(port, host);

  console.log(`🚀 Job Platform API is running on: http://${host}:${port}`);
  console.log(`📚 API Documentation available at: http://${host}:${port}/api/docs`);
  console.log(`🔥 Firebase Admin SDK ready for production authentication`);
}
bootstrap();
