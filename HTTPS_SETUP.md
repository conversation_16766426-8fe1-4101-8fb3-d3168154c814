# HTTPS Setup Guide for Tuchanga Job Platform

This guide explains how to set up HTTPS for your Tuchanga job platform on AWS EC2.

## 🎯 Overview

Your app will be accessible at: `https://ec2-18-231-110-178.sa-east-1.compute.amazonaws.com`

## 🚀 Quick Setup (Self-Signed Certificate)

For immediate HTTPS setup with self-signed certificates:

```bash
# Make script executable
chmod +x generate-ssl.sh

# Generate SSL certificates
./generate-ssl.sh

# Start the application
docker-compose up -d --build
```

**Note**: Browsers will show security warnings for self-signed certificates. Click "Advanced" → "Proceed to site" to continue.

## 🔒 Production Setup (Let's Encrypt)

For production with valid SSL certificates:

### Prerequisites
- Domain must point to your EC2 instance
- Port 80 and 443 must be open in security groups
- Email address for certificate notifications

### Steps

1. **Make script executable**:
   ```bash
   chmod +x setup-https.sh
   ```

2. **Run setup script**:
   ```bash
   ./setup-https.sh
   ```

3. **Choose option 2** (Let's Encrypt)

4. **Follow prompts** to obtain certificate

## 🛠️ Manual Certificate Setup

If you have your own SSL certificates:

1. Create SSL directory:
   ```bash
   mkdir -p ssl
   ```

2. Copy your certificates:
   ```bash
   cp your-certificate.pem ssl/cert.pem
   cp your-private-key.pem ssl/key.pem
   ```

3. Set permissions:
   ```bash
   chmod 644 ssl/cert.pem
   chmod 600 ssl/key.pem
   ```

4. Start application:
   ```bash
   docker-compose up -d --build
   ```

## 🔧 Configuration Changes Made

### 1. Nginx Configuration
- Added HTTPS server block (port 443)
- HTTP to HTTPS redirect
- SSL certificate configuration
- Security headers

### 2. Backend CORS
- Added HTTPS origins to allowed list
- Supports both HTTP (dev) and HTTPS (prod)

### 3. Frontend Environment
- Automatic HTTPS detection
- Uses nginx proxy for API calls in HTTPS mode

### 4. Docker Compose
- Exposed port 443
- SSL certificate volume mount

## 🌐 AWS Security Group Configuration

Ensure your EC2 security group allows:

| Type  | Protocol | Port | Source    | Description |
|-------|----------|------|-----------|-------------|
| HTTP  | TCP      | 80   | 0.0.0.0/0 | HTTP access |
| HTTPS | TCP      | 443  | 0.0.0.0/0 | HTTPS access |
| Custom| TCP      | 3000 | 0.0.0.0/0 | Backend API |

## 🔄 Certificate Renewal (Let's Encrypt)

Let's Encrypt certificates expire every 90 days. Set up auto-renewal:

```bash
# Edit crontab
sudo crontab -e

# Add renewal job (runs daily at noon)
0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart frontend
```

## 🧪 Testing HTTPS Setup

1. **Check certificate**:
   ```bash
   openssl x509 -in ssl/cert.pem -text -noout
   ```

2. **Test HTTPS connection**:
   ```bash
   curl -k https://ec2-18-231-110-178.sa-east-1.compute.amazonaws.com
   ```

3. **Check container status**:
   ```bash
   docker-compose ps
   ```

4. **View logs**:
   ```bash
   docker-compose logs frontend
   docker-compose logs backend
   ```

## 🐛 Troubleshooting

### Certificate Issues
- **File not found**: Ensure certificates are in `ssl/` directory
- **Permission denied**: Check file permissions (644 for cert, 600 for key)
- **Invalid certificate**: Verify with `openssl x509 -in ssl/cert.pem -text -noout`

### CORS Issues
- **Access blocked**: Check backend logs for CORS errors
- **Wrong origin**: Verify HTTPS URL is in CORS allowed origins

### Connection Issues
- **Port not accessible**: Check AWS security groups
- **Container not starting**: Check `docker-compose logs frontend`
- **502 Bad Gateway**: Backend might not be ready, check `docker-compose logs backend`

## 📱 Frontend API Configuration

The frontend automatically detects HTTPS and adjusts API calls:

- **HTTPS mode**: Uses `https://your-domain/api/` (proxied through nginx)
- **HTTP mode**: Uses `http://your-domain:3000/` (direct backend connection)

## 🔐 Security Features

- **TLS 1.2/1.3**: Modern encryption protocols
- **HSTS**: HTTP Strict Transport Security
- **Security headers**: X-Frame-Options, X-Content-Type-Options, etc.
- **Automatic HTTP redirect**: All HTTP traffic redirected to HTTPS

## 📋 Next Steps

1. **Test the application**: Visit `https://ec2-18-231-110-178.sa-east-1.compute.amazonaws.com`
2. **Update DNS**: Point your custom domain to the EC2 instance
3. **Monitor certificates**: Set up renewal notifications
4. **Backup certificates**: Store certificates securely

## 🆘 Support

If you encounter issues:
1. Check container logs: `docker-compose logs`
2. Verify certificate files exist and have correct permissions
3. Ensure AWS security groups allow HTTPS traffic
4. Test with curl: `curl -k https://your-domain`
